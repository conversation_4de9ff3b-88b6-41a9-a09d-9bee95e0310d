.buyKami[data-v-0df41fa3] {
    color: #409eff;
    margin-bottom: 10px
}

.explain[data-v-4a0b1add] {
    background-color: #fdf6ec;
    color: #e6a23c;
    padding: 8px 16px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin-top: 10px
}

.title[data-v-798b5e1c] {
    margin-top: 20px;
    display: block;
    font-size: 16px;
    color: #75787e;
    padding-left: 20px;
    font-weight: 700
}

.noData[data-v-798b5e1c] {
    margin-top: 20px;
    padding-left: 20px;
    font-size: 14px;
    color: #72767b;
    display: block
}

.clear[data-v-798b5e1c] {
    font-size: 14px;
    color: #f13e3e;
    cursor: pointer
}

.historyDataList[data-v-798b5e1c] {
    padding: 0 20px;
    margin: 20px 0;
    max-height: 50%;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box
}

.title[data-v-1cea6887] {
    margin-top: 20px;
    display: block;
    font-size: 16px;
    color: #75787e;
    padding-left: 20px;
    font-weight: 700
}

.content[data-v-1cea6887] {
    padding: 20px
}

.item[data-v-1cea6887] {
    display: flex;
    justify-content: space-between
}

.date[data-v-1cea6887] {
    color: #75787e;
    font-weight: 400
}

.blue[data-v-1cea6887],
.priceFive[data-v-1cea6887] {
    color: #00f
}

.priceSix[data-v-1cea6887] {
    color: #00be32
}

.orange[data-v-1cea6887],
.priceSeven[data-v-1cea6887] {
    color: #ff7d00
}

.priceEight[data-v-1cea6887],
.purple[data-v-1cea6887] {
    color: #fa44e6
}

.priceNine[data-v-1cea6887],
.red[data-v-1cea6887] {
    color: red
}

.priceTen[data-v-1cea6887],
.yellow[data-v-1cea6887] {
    color: #e8ce03
}

.priceEleven[data-v-1cea6887] {
    color: #14ddd6
}

.green[data-v-1cea6887] {
    color: #60ff00
}

.tagBox[data-v-357cf6b5] {
    max-height: 300px;
    overflow-y: auto
}

.tagItem[data-v-357cf6b5] {
    margin-right: 10px
}

.title[data-v-4487bbf1] {
    margin-top: 20px;
    display: block;
    font-size: 16px;
    color: #75787e;
    padding-left: 20px;
    font-weight: 700
}

.noData[data-v-4487bbf1] {
    margin-top: 20px;
    padding-left: 20px;
    font-size: 14px;
    color: #72767b;
    display: block
}

.clear[data-v-4487bbf1] {
    font-size: 14px;
    color: #f13e3e;
    cursor: pointer
}

.historyDataList[data-v-4487bbf1] {
    padding: 0 20px;
    margin: 20px 0;
    max-height: 50%;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box
}

@media (max-width: 767px) {
    .el-drawer {
        width: 60% !important
    }
}

.drawerBox[data-v-410c228a] {
    padding: 20px
}

.attrInput[data-v-410c228a] {
    width: 100%
}

.bottomGap[data-v-410c228a] {
    margin-bottom: 10px
}

.rightGap[data-v-410c228a] {
    margin-right: 10px
}

.el-scrollbar .el-scrollbar__bar {
    opacity: 1 !important
}

@media (max-width: 767px) {
    .el-drawer {
        width: 80% !important
    }
}

.red {
    color: red
}

.lingzhuBlue {
    color: #44b6f8
}

.lingzhuYellow {
    color: #ebd906
}

.lingzhuGreen {
    color: #00e900
}

.fontBoldStroke[data-v-041bb81a],
.fontStroke[data-v-041bb81a] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-041bb81a] {
    font-weight: 700
}

.name[data-v-041bb81a] {
    display: flex;
    align-items: center;
    line-height: 16px
}

.rightGap[data-v-041bb81a] {
    margin-right: 10px
}

.topGap[data-v-041bb81a] {
    margin-top: 10px;
    position: relative
}

.iconColor[data-v-041bb81a] {
    color: #ffa801
}

.attrBox[data-v-041bb81a] {
    position: absolute;
    top: 0;
    left: 200px
}

.attrItem[data-v-041bb81a] {
    white-space: nowrap
}

.gray[data-v-041bb81a] {
    color: #fff
}

.blue[data-v-041bb81a] {
    color: #00c0ff
}

.green[data-v-041bb81a] {
    color: #60ff00
}

.subGreen[data-v-041bb81a] {
    color: #28f049
}

.orange[data-v-041bb81a] {
    color: #ff7d00
}

.purple[data-v-041bb81a] {
    color: #fa44e6
}

.red[data-v-041bb81a] {
    color: red
}

.yellow[data-v-041bb81a] {
    color: #f1f103
}

.cyan[data-v-041bb81a] {
    color: #a0eb91
}

.brown[data-v-041bb81a] {
    color: #b0762e
}

.yuanshenDetail .gray {
    color: #fff
}

.yuanshenDetail .blue {
    color: #00c0ff
}

.yuanshenDetail .green {
    color: #60ff00
}

.yuanshenDetail .purple {
    color: #dd56ff
}

.fontBoldStroke[data-v-392915df],
.fontStroke[data-v-392915df] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-392915df] {
    font-weight: 700
}

.yuanshenDetail[data-v-392915df] {
    white-space: nowrap;
    box-sizing: border-box
}

.yuanshenInfo[data-v-392915df] {
    display: flex;
    align-items: center
}

.yuanshenInfoItem[data-v-392915df] {
    margin-right: 10px;
    display: flex
}

.yuanshenDataBox[data-v-392915df] {
    background-color: rgba(1, 34, 48, .8);
    border-radius: 10px;
    padding: 5px;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box
}

.yuanshenDataBox .fuseContent[data-v-392915df] {
    flex-grow: 0
}

.fuseContent.yuanshenInfoGrow[data-v-392915df] {
    display: flex;
    justify-content: space-between;
    text-align: left;
    padding: 0 5px 0 10px;
    box-sizing: border-box;
    flex-grow: 1
}

.commonLabelBox[data-v-392915df] {
    position: relative;
    z-index: 2
}

.commonLabel[data-v-392915df] {
    background: linear-gradient(#ffd58d, #ffad37);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    font-weight: 700;
    display: inline-block
}

.commonLabel[data-v-392915df]:after {
    content: attr(data-label);
    position: absolute;
    left: 0;
    top: 0;
    text-shadow: 1px 1px 1px #081c29, -1px -1px 1px #081c29, -1px 1px 1px #081c29, 1px -1px 1px #081c29;
    z-index: -1
}

.commonLabel.yuanshenData[data-v-392915df] {
    background: linear-gradient(#fcf8bd, #ff9e03);
    -webkit-background-clip: text
}

.wugong[data-v-392915df] {
    background: linear-gradient(#ffa66e, #f15f59);
    -webkit-background-clip: text;
    position: relative
}

.mogong[data-v-392915df] {
    background: linear-gradient(#f580d3, #9a4cc3);
    -webkit-background-clip: text
}

.wufang[data-v-392915df] {
    background: linear-gradient(#03bcff, #4e61ff);
    -webkit-background-clip: text
}

.mofang[data-v-392915df] {
    background: linear-gradient(#6cf3ff, #0baaff);
    -webkit-background-clip: text
}

.gongpei[data-v-392915df] {
    background: linear-gradient(#a1ee67, #77ba0b);
    -webkit-background-clip: text
}

.fangpei[data-v-392915df] {
    background: linear-gradient(#f0e967, #eeb40b);
    -webkit-background-clip: text
}

.minRightGap[data-v-392915df] {
    margin-right: 5px
}

.rightGap[data-v-392915df] {
    margin-right: 10px
}

.topGap[data-v-392915df] {
    margin-top: 10px
}

.imgList[data-v-392915df] {
    display: flex;
    margin-right: 10px
}

.imgBox[data-v-392915df] {
    width: 25px;
    height: 25px;
    margin-right: 5px;
    position: relative
}

.img[data-v-392915df] {
    width: 100%;
    height: 100%;
    border-radius: 2px
}

.stuntLevel[data-v-392915df] {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 12px;
    line-height: 12px;
    color: #f9ff00
}

.skillInfoBox[data-v-392915df] {
    position: absolute;
    background: rgba(4, 36, 45, .95);
    bottom: 100%;
    left: 0;
    border-radius: 5px;
    z-index: 30;
    padding: 10px;
    border: 1px solid #07caef;
    font-size: 14px;
    box-sizing: border-box;
    white-space: normal
}

.skillDesc[data-v-392915df] {
    margin-top: 5px;
    margin-bottom: 0
}

.specialSkillTitle[data-v-392915df] {
    background: linear-gradient(#ffcef0, #ff8afa);
    -webkit-background-clip: text
}

.fuseItemRow[data-v-392915df] {
    display: flex;
    margin-bottom: 5px
}

.fuseItem[data-v-392915df] {
    display: flex;
    align-items: center;
    margin-right: 5px;
    line-height: 19px;
    width: 19%;
    justify-content: space-between
}

.fuseItem[data-v-392915df]:last-child {
    margin-right: 0
}

.fuseIcon[data-v-392915df] {
    width: 17px;
    height: 17px;
    font-size: 12px;
    border: 2px solid #000b10;
    background: linear-gradient(#fcb18e, #b03533);
    font-weight: 700;
    color: #000b10;
    text-align: center;
    line-height: 17px;
    border-radius: 5px;
    position: relative;
    margin-right: 5px
}

.fuseIcon[data-v-392915df]:after {
    content: "";
    display: block;
    width: 2px;
    height: 2px;
    background: #fff;
    position: absolute;
    top: 1px;
    left: 1px
}

.fuseContent[data-v-392915df] {
    color: #f9ff00;
    text-align: center;
    border-radius: 10px;
    background: rgba(0, 14, 19, .8);
    font-size: 12px;
    border-bottom: 1px solid #355660;
    height: 18px;
    line-height: 19px;
    flex-grow: 1
}

.fuseContent.totalFuse[data-v-392915df] {
    width: 68px
}

.fuseItem.totalFuse[data-v-392915df] {
    width: 88px
}

.fuseBox[data-v-392915df] {
    width: 100%;
    max-width: 360px;
    box-sizing: border-box;
    margin-top: 10px;
    border-top: 1px dashed #05c0dd;
    border-bottom: 1px dashed #05c0dd;
    padding: 5px 5px 0;
    border-radius: 5px
}

.closeIcon[data-v-392915df] {
    position: absolute;
    right: 10px;
    top: 10px;
    display: none
}

@media (max-width: 767px) {
    .flexWrapSm[data-v-392915df] {
        flex-wrap: wrap
    }

    .yuanshenDataBox[data-v-392915df] {
        font-size: 12px
    }

    .closeIcon[data-v-392915df] {
        display: block
    }
}

.fontBoldStroke[data-v-1867aba4],
.fontStroke[data-v-1867aba4] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-1867aba4] {
    font-weight: 700
}

.infoBox[data-v-1867aba4] {
    height: 16px;
    line-height: 16px
}

.rightGap[data-v-1867aba4] {
    margin-right: 10px
}

.topGap[data-v-1867aba4] {
    margin-top: 10px
}

.commonLabelBox[data-v-1867aba4] {
    position: relative;
    z-index: 2;
    white-space: nowrap
}

.commonLabel[data-v-1867aba4] {
    background: linear-gradient(#fcf8bd, #ff9e03);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    font-weight: 700;
    display: inline-block
}

.commonLabel[data-v-1867aba4]:after {
    content: attr(data-label);
    position: absolute;
    left: 0;
    top: 0;
    text-shadow: 1px 1px 1px #672c00, -1px -1px 1px #672c00, -1px 1px 1px #672c00, 1px -1px 1px #672c00;
    z-index: -1
}

.talentTitle[data-v-1867aba4] {
    line-height: 25px;
    font-size: 18px
}

.aoyiInfoBox[data-v-1867aba4] {
    background: rgba(1, 20, 27, .8);
    border: 1px solid #0a3a49;
    border-radius: 5px;
    padding: 0 5px;
    max-width: 360px;
    box-sizing: border-box
}

.aoyiInfoItem[data-v-1867aba4] {
    display: flex;
    padding: 5px 0 0;
    border-bottom: 1px solid #354349;
    line-height: 25px
}

.aoyiInfoItem[data-v-1867aba4]:last-child {
    border-bottom: none
}

.imgList[data-v-1867aba4] {
    display: flex;
    flex-wrap: wrap
}

.imgBox[data-v-1867aba4] {
    width: 25px;
    height: 25px;
    margin: 0 5px 5px 0;
    position: relative
}

.img[data-v-1867aba4] {
    width: 100%;
    height: 100%;
    border-radius: 2px
}

.skillInfoBox[data-v-1867aba4] {
    width: 222px;
    position: absolute;
    background: rgba(4, 36, 45, .95);
    bottom: 100%;
    left: 0;
    border-radius: 5px;
    z-index: 30;
    padding: 10px;
    border: 1px solid #07caef;
    font-size: 14px;
    box-sizing: border-box;
    white-space: normal
}

.skillDesc[data-v-1867aba4] {
    margin-top: 5px;
    margin-bottom: 0
}

.skillName[data-v-1867aba4] {
    text-align: center;
    color: #f9ff00;
    display: block
}

.closeIcon[data-v-1867aba4] {
    position: absolute;
    right: 10px;
    top: 10px;
    display: none
}

@media (max-width: 767px) {
    .closeIcon[data-v-1867aba4] {
        display: block
    }
}

.fontStroke[data-v-8f35dd0c] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.dataItem[data-v-8f35dd0c] {
    font-size: 14px
}

.lingpoDetail .gray {
    color: #fff
}

.lingpoDetail .blue {
    color: #00c0ff
}

.lingpoDetail .green {
    color: #60ff00
}

.lingpoDetail .orange {
    color: #ff7d00
}

.lingpoDetail .purple {
    color: #fa44e6
}

.lingpoDetail .red {
    color: red
}

.lingpoDetail .yellow {
    color: #f1f103
}

.lingpoDetail .cyan {
    color: #14ddd6
}

.lingpoDetail .brown {
    color: #b0762e
}

.commonLabelBox[data-v-6f98ec1e] {
    position: relative;
    z-index: 2;
    white-space: nowrap
}

.commonLabel[data-v-6f98ec1e] {
    background: linear-gradient(#fcf8bd, #ff9e03);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    font-weight: 700
}

.shadow[data-v-6f98ec1e] {
    position: absolute;
    left: 0;
    top: 0;
    text-shadow: 1px 1px 1px #672c00, -1px -1px 1px #672c00, -1px 1px 1px #672c00, 1px -1px 1px #672c00;
    z-index: -1
}

.fontBoldStroke[data-v-3398507c],
.fontStroke[data-v-3398507c] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-3398507c] {
    font-weight: 700
}

.lingshouInfoBox[data-v-3398507c] {
    display: flex;
    font-size: 14px;
    align-items: center;
    line-height: 20px;
    white-space: nowrap
}

.lingshouInfoCol[data-v-3398507c] {
    margin-right: 20px;
    display: flex
}

.levelContent[data-v-3398507c] {
    color: #caec26
}

.lingshouSubTitle[data-v-3398507c] {
    background: linear-gradient(#fff, #20c4f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    font-weight: 700;
    text-align: center;
    margin-top: 10px;
    font-size: 15px;
    display: inline-block
}

.lingshouSubTitle[data-v-3398507c]:after {
    content: attr(data-label);
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1
}

.attrContent[data-v-3398507c] {
    display: flex
}

.attrItem[data-v-3398507c] {
    display: flex;
    align-items: center;
    margin-right: 20px
}

.attrItemContent[data-v-3398507c] {
    color: #f2df06;
    font-size: 14px;
    display: flex;
    align-items: center
}

.extraValue[data-v-3398507c] {
    color: #35bc34;
    margin-left: 5px
}

.bianyiBox[data-v-3398507c] {
    display: flex
}

.bianyiLabel[data-v-3398507c] {
    color: #c04bdd
}

.bianyiContent[data-v-3398507c] {
    color: #dd56ff
}

.imgList[data-v-3398507c] {
    display: flex;
    flex-wrap: wrap
}

.imgBox[data-v-3398507c] {
    width: 25px;
    height: 25px;
    margin: 0 5px 5px 0;
    position: relative
}

.img[data-v-3398507c] {
    width: 100%;
    height: 100%;
    border-radius: 2px
}

.skillLevel[data-v-3398507c] {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 12px;
    line-height: 12px;
    color: #f9ff00
}

.skillInfoBox[data-v-3398507c] {
    width: 222px;
    position: absolute;
    background: rgba(4, 36, 45, .95);
    bottom: 100%;
    left: 0;
    border-radius: 5px;
    z-index: 30;
    padding: 10px;
    border: 1px solid #07caef;
    font-size: 14px;
    box-sizing: border-box;
    white-space: normal
}

.skillDesc[data-v-3398507c] {
    margin-top: 5px;
    margin-bottom: 0
}

.skillName[data-v-3398507c] {
    text-align: center;
    color: #f9ff00;
    display: block
}

.jibanContent[data-v-3398507c] {
    line-height: 20px
}

.jibanTitle[data-v-3398507c] {
    color: #f6f1ba
}

.jibanDesc[data-v-3398507c] {
    color: #fff;
    margin-left: 15px
}

.noData[data-v-3398507c],
.white[data-v-3398507c] {
    color: #fff
}

.green[data-v-3398507c] {
    color: #00be32
}

.blue[data-v-3398507c] {
    color: #00c0ff
}

.orange[data-v-3398507c] {
    color: #ff7d00
}

.purple[data-v-3398507c] {
    color: #fa44e6
}

.closeIcon[data-v-3398507c] {
    position: absolute;
    right: 10px;
    top: 10px;
    display: none
}

@media (max-width: 767px) {
    .closeIcon[data-v-3398507c] {
        display: block
    }

    .lingshouInfoBox[data-v-3398507c] {
        flex-wrap: wrap
    }
}

.green {
    color: #00be32
}

.blue {
    color: #00c0ff
}

.orange {
    color: #ff7d00
}

.purple {
    color: #fa44e6
}

.fontBoldStroke[data-v-3c0e9bf3],
.fontStroke[data-v-3c0e9bf3] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-3c0e9bf3] {
    font-weight: 700
}

.bazhenInfoBox[data-v-3c0e9bf3],
.scoreBox[data-v-3c0e9bf3] {
    display: flex;
    align-items: center
}

.scoreBox[data-v-3c0e9bf3] {
    font-size: 14px;
    margin-right: 30px
}

.scoreTitle[data-v-3c0e9bf3] {
    color: #fcbd70
}

.stateBox[data-v-3c0e9bf3] {
    display: flex;
    align-items: center
}

.stateTitle[data-v-3c0e9bf3] {
    background: linear-gradient(#fff, #20c4f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    font-weight: 700;
    text-align: center;
    font-size: 15px
}

.stateTitle[data-v-3c0e9bf3]:after {
    content: attr(data-label);
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1
}

.stateContent[data-v-3c0e9bf3] {
    font-size: 14px
}

.bazhenContentBox[data-v-3c0e9bf3] {
    margin-top: 10px
}

.bazhenItem[data-v-3c0e9bf3] {
    margin-bottom: 10px;
    font-size: 14px;
    display: flex;
    align-items: center
}

.square[data-v-3c0e9bf3] {
    margin: 0 10px;
    width: 10px;
    height: 10px;
    transform: rotate(45deg);
    border-radius: 2px
}

.square.green[data-v-3c0e9bf3] {
    background: #26b01d;
    box-shadow: inset 0 0 5px hsla(0, 0%, 100%, .5)
}

.square.blue[data-v-3c0e9bf3] {
    background: #2093da;
    box-shadow: inset 0 0 5px hsla(0, 0%, 100%, .5)
}

.square.yellow[data-v-3c0e9bf3] {
    background: #f1c42b;
    box-shadow: inset 0 0 5px hsla(0, 0%, 100%, .5)
}

.square.orange[data-v-3c0e9bf3] {
    background: #eb600c;
    box-shadow: inset 0 0 5px hsla(0, 0%, 100%, .5)
}

.itemName[data-v-3c0e9bf3] {
    margin-right: 15px
}

.itemContentBox[data-v-3c0e9bf3] {
    display: flex;
    align-items: center
}

.rank[data-v-3c0e9bf3] {
    margin-right: 30px
}

.attr[data-v-3c0e9bf3] {
    width: 100px;
    white-space: nowrap
}

.value[data-v-3c0e9bf3] {
    color: #34ed3a
}

.notActive[data-v-3c0e9bf3],
.range[data-v-3c0e9bf3] {
    color: #9d9c9a
}

.fontBoldStroke[data-v-c36c67d0],
.fontStroke[data-v-c36c67d0] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-c36c67d0] {
    font-weight: 700
}

.detailBox[data-v-c36c67d0] {
    font-size: 14px;
    padding-top: 5px
}

.shopDetailItem[data-v-c36c67d0] {
    line-height: 25px;
    font-size: 14px;
    text-align: left
}

.gray[data-v-c36c67d0] {
    color: #505152
}

.blue[data-v-c36c67d0] {
    color: #006fa6
}

.green[data-v-c36c67d0] {
    color: #00be32
}

.orange[data-v-c36c67d0] {
    color: #ff7d00
}

.purple[data-v-c36c67d0] {
    color: #fa44e6
}

.red[data-v-c36c67d0] {
    color: #d00
}

.yellow[data-v-c36c67d0] {
    color: #f1f103
}

.cyan[data-v-c36c67d0] {
    color: #14ddd6
}

.brown[data-v-c36c67d0] {
    color: #b0762e
}

.shopAttrBox[data-v-c36c67d0] {
    line-height: 25px;
    font-size: 41px
}

.equipSpan[data-v-c36c67d0] {
    margin-right: 10px
}

.sameBoothBox[data-v-b482a39c] {
    background: #f0f0f0;
    height: 100%;
    box-sizing: border-box
}

.sameBoothContent[data-v-b482a39c] {
    width: 1080px;
    height: 100%;
    margin: 0 auto 0;
    background: #fff;
    box-sizing: border-box
}

.title[data-v-b482a39c] {
    padding-top: 20px;
    display: block;
    font-size: 16px;
    color: #75787e;
    padding-left: 20px;
    font-weight: 700;
    line-height: 20px
}

.list[data-v-b482a39c] {
    list-style: none;
    height: calc(100% - 60px);
    overflow-y: auto;
    padding: 0 20px 20px;
    margin: 20px 0 0;
    box-sizing: border-box
}

.dataItem[data-v-b482a39c] {
    padding: 5px 20px;
    background: rgba(1, 33, 44, .8);
    border-radius: 5px;
    border: 1px solid #01c5e8;
    box-sizing: border-box;
    color: #fff;
    margin-top: 5px;
    line-height: 20px;
    font-size: 14px;
    overflow-x: auto
}

.ellipsis[data-v-b482a39c] {
    overflow: hidden;
    text-overflow: ellipsis
}

.dataRow[data-v-b482a39c] {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.dataRow span[data-v-b482a39c] {
    display: inline-block;
    line-height: 20px;
    vertical-align: top;
    white-space: nowrap
}

.dataTitle[data-v-b482a39c] {
    color: #ffff64;
    width: 320px
}

.dataName[data-v-b482a39c],
.dataTitle.short[data-v-b482a39c] {
    width: 200px
}

.shopNum[data-v-b482a39c] {
    width: 55px
}

.nameTitle[data-v-b482a39c] {
    background: linear-gradient(#b6f5ff, #2feeff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 700
}

.shopNameContent[data-v-b482a39c] {
    width: 200px
}

.goodsName[data-v-b482a39c] {
    max-width: 150px
}

.shopDate[data-v-b482a39c] {
    font-size: 12px;
    align-items: center;
    display: flex;
    color: #f69323
}

.packUp[data-v-b482a39c] {
    color: #c8c9cc;
    font-size: 12px
}

.packUp .date[data-v-b482a39c] {
    color: #c8c9cc;
    font-weight: 400
}

.packUpLabel[data-v-b482a39c] {
    margin-left: 5px
}

.date[data-v-b482a39c] {
    background: linear-gradient(#f69323, #fae579);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 700
}

.count[data-v-b482a39c] {
    color: #fff;
    margin-left: 5px;
    font-size: 12px;
    vertical-align: baseline !important;
    line-height: 12px !important
}

.price[data-v-b482a39c] {
    width: 180px;
    display: inline-flex !important;
    align-items: center;
    justify-content: flex-end
}

.iconShuJu[data-v-b482a39c] {
    font-size: 12px !important;
    margin-left: 5px;
    color: red
}

.priceFour[data-v-b482a39c] {
    color: #fff
}

.blue[data-v-b482a39c],
.priceFive[data-v-b482a39c] {
    color: #00c0ff
}

.priceSix[data-v-b482a39c] {
    color: #00be32
}

.orange[data-v-b482a39c],
.priceSeven[data-v-b482a39c] {
    color: #ff7d00
}

.priceEight[data-v-b482a39c],
.purple[data-v-b482a39c] {
    color: #fa44e6
}

.priceNine[data-v-b482a39c],
.red[data-v-b482a39c] {
    color: red
}

.priceTen[data-v-b482a39c],
.yellow[data-v-b482a39c] {
    color: #f1f103
}

.priceEleven[data-v-b482a39c] {
    color: #14ddd6
}

.green[data-v-b482a39c] {
    color: #60ff00
}

@media (max-width: 767px) {
    .sameBoothBox[data-v-b482a39c] {
        padding: 0
    }

    .sameBoothContent[data-v-b482a39c] {
        width: 100%
    }

    .hiddenSm[data-v-b482a39c] {
        display: none
    }

    .flexWrapSm[data-v-b482a39c] {
        flex-wrap: wrap
    }

    .title[data-v-b482a39c] {
        padding-left: 10px
    }

    .list[data-v-b482a39c] {
        padding: 0 10px 10px;
        margin-top: 10px
    }

    .dataName[data-v-b482a39c],
    .dataTitle.short[data-v-b482a39c],
    .dataTitle[data-v-b482a39c] {
        width: 100%
    }

    .shopNameContent[data-v-b482a39c] {
        width: auto
    }

    .goodsName[data-v-b482a39c],
    .price[data-v-b482a39c] {
        max-width: 120px
    }

    .shopList .dataTitle[data-v-b482a39c] {
        width: 100%
    }

    .shopList .dataName[data-v-b482a39c] {
        width: calc(100% - 55px);
        text-align: right
    }

    .dataItem[data-v-b482a39c] {
        padding: 5px 10px
    }
}

@media (max-width: 767px) {
    .sameBoothDrawer .el-drawer {
        width: 100% !important
    }
}

.tagBox[data-v-98d32206] {
    max-height: 300px;
    overflow-y: auto
}

.tagItem[data-v-98d32206] {
    margin-right: 10px
}

.nameItem[data-v-0537fc54] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px
}

.explain[data-v-0537fc54] {
    background-color: #fdf6ec;
    color: #e6a23c;
    padding: 8px 16px;
    border-radius: 4px
}

.list[data-v-5f9abf1e] {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px
}

.item[data-v-5f9abf1e] {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px
}

.item[data-v-5f9abf1e]:last-child {
    margin-bottom: 0
}

.count[data-v-5f9abf1e] {
    margin-left: 5px
}

.explain[data-v-5f9abf1e] {
    background-color: #fdf6ec;
    color: #e6a23c;
    padding: 8px 16px;
    border-radius: 4px
}

.priceFour[data-v-5f9abf1e] {
    color: #fff
}

.blue[data-v-5f9abf1e],
.priceFive[data-v-5f9abf1e] {
    color: #00c0ff
}

.priceSix[data-v-5f9abf1e] {
    color: #00be32
}

.orange[data-v-5f9abf1e],
.priceSeven[data-v-5f9abf1e] {
    color: #ff7d00
}

.priceEight[data-v-5f9abf1e],
.purple[data-v-5f9abf1e] {
    color: #fa44e6
}

.priceNine[data-v-5f9abf1e],
.red[data-v-5f9abf1e] {
    color: red
}

.priceTen[data-v-5f9abf1e],
.yellow[data-v-5f9abf1e] {
    color: #f1f103
}

.priceEleven[data-v-5f9abf1e] {
    color: #14ddd6
}

.el-backtop {
    position: absolute
}

.row-expand-cover .el-table__expand-icon {
    visibility: hidden
}

.el-dropdown {
    vertical-align: top
}

.el-dropdown+.el-dropdown {
    margin-left: 15px
}

.el-icon-arrow-down {
    font-size: 12px
}

.dataList .el-tabs {
    height: 100%
}

.dataList .el-tabs__header {
    margin: 0
}

.dataList .el-tabs__content {
    height: calc(100% - 40px)
}

.dataList .el-tab-pane {
    height: 100%
}

.smRadioBox .el-radio {
    margin-right: 10px
}

.smRadioBox .el-radio__label {
    font-size: 12px
}

.smRadioBox .el-radio__inner {
    width: 12px;
    height: 12px
}

@media (max-width: 767px) {
    .el-message-box {
        width: 300px !important
    }
}

.fontBoldStroke[data-v-20b39bfa],
.fontStroke[data-v-20b39bfa] {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke[data-v-20b39bfa] {
    font-weight: 700
}

.swiperBox[data-v-20b39bfa] {
    height: auto;
    margin-bottom: 20px
}

.swiperImg[data-v-20b39bfa] {
    width: 100%
}

.dateLabel[data-v-20b39bfa] {
    width: 90px;
    display: inline-block
}

.indexBox[data-v-20b39bfa] {
    padding: 20px 20px 0;
    box-sizing: border-box;
    height: 100%;
    position: relative
}

.flexRow[data-v-20b39bfa] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 10px
}

.notice[data-v-20b39bfa] {
    color: red;
    margin-bottom: 0;
    font-size: 14px
}

.notice[data-v-20b39bfa]:last-child {
    margin-bottom: 10px
}

.message[data-v-20b39bfa],
.regionLabel[data-v-20b39bfa] {
    font-size: 14px
}

.dataList[data-v-20b39bfa] {
    margin-top: 10px
}

.infinite-list-wrapper[data-v-20b39bfa] {
    height: 100%;
    overflow-y: auto
}

.list[data-v-20b39bfa] {
    list-style: none;
    padding: 0;
    margin: 0
}

.dataItem[data-v-20b39bfa] {
    padding: 5px 20px;
    background: rgba(1, 33, 44, .8);
    border-radius: 5px;
    border: 1px solid #01c5e8;
    box-sizing: border-box;
    color: #fff;
    margin-top: 5px;
    line-height: 20px;
    font-size: 14px;
    overflow-x: auto
}

.ellipsis[data-v-20b39bfa] {
    overflow: hidden;
    text-overflow: ellipsis
}

.dataRow[data-v-20b39bfa] {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.dataRow span[data-v-20b39bfa] {
    display: inline-block;
    line-height: 20px;
    vertical-align: top;
    white-space: nowrap
}

.dataTitle[data-v-20b39bfa] {
    color: #ffff64;
    width: 320px
}

.dataName[data-v-20b39bfa],
.dataTitle.short[data-v-20b39bfa] {
    width: 200px
}

.shopNum[data-v-20b39bfa] {
    width: 55px
}

.nameTitle[data-v-20b39bfa] {
    background: linear-gradient(#b6f5ff, #2feeff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 700
}

.shopNameContent[data-v-20b39bfa] {
    width: 200px
}

.goodsName[data-v-20b39bfa] {
    max-width: 150px
}

.shopDate[data-v-20b39bfa] {
    font-size: 12px;
    align-items: center;
    display: flex;
    color: #f69323
}

.packUp[data-v-20b39bfa] {
    color: #c8c9cc;
    font-size: 12px
}

.packUp .date[data-v-20b39bfa] {
    color: #c8c9cc;
    font-weight: 400
}

.packUpLabel[data-v-20b39bfa] {
    margin-left: 5px
}

.date[data-v-20b39bfa] {
    background: linear-gradient(#f69323, #fae579);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 700
}

.sameBooth[data-v-20b39bfa] {
    line-height: 20px;
    color: #ffe08b;
    white-space: nowrap;
    margin-right: 20px;
    cursor: pointer
}

.count[data-v-20b39bfa] {
    color: #fff;
    margin-left: 5px;
    font-size: 12px;
    vertical-align: baseline !important;
    line-height: 12px !important
}

.price[data-v-20b39bfa] {
    width: 180px;
    display: inline-flex !important;
    align-items: center;
    justify-content: flex-end
}

.sgPrice[data-v-20b39bfa] {
    color: #f7e867;
    margin-left: 5px
}

.iconShuJu[data-v-20b39bfa] {
    font-size: 12px !important;
    margin-left: 5px;
    color: red
}

.noMore[data-v-20b39bfa] {
    color: #909399;
    text-align: center
}

.expandIconBox[data-v-20b39bfa] {
    position: absolute;
    background-color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: 0 0 6px rgba(0 0 0 .12);
    cursor: pointer;
    z-index: 5;
    bottom: 120px;
    right: -100px;
    color: #409eff
}

.expandIconBox[data-v-20b39bfa]:hover {
    background-color: #f2f6fc
}

.priceFour[data-v-20b39bfa] {
    color: #fff
}

.blue[data-v-20b39bfa],
.priceFive[data-v-20b39bfa] {
    color: #00c0ff
}

.priceSix[data-v-20b39bfa] {
    color: #00be32
}

.orange[data-v-20b39bfa],
.priceSeven[data-v-20b39bfa] {
    color: #ff7d00
}

.priceEight[data-v-20b39bfa],
.purple[data-v-20b39bfa] {
    color: #fa44e6
}

.priceNine[data-v-20b39bfa],
.red[data-v-20b39bfa] {
    color: red
}

.priceTen[data-v-20b39bfa],
.yellow[data-v-20b39bfa] {
    color: #f1f103
}

.priceEleven[data-v-20b39bfa] {
    color: #14ddd6
}

.green[data-v-20b39bfa] {
    color: #60ff00
}

.btnBox[data-v-20b39bfa] {
    float: right
}

.cursor[data-v-20b39bfa] {
    cursor: pointer
}

.smRadioBox[data-v-20b39bfa] {
    margin-top: 10px
}

.radioLabel[data-v-20b39bfa] {
    font-size: 12px;
    margin-right: 10px;
    color: #606266
}

@media (max-width: 767px) {
    .indexBox[data-v-20b39bfa] {
        padding: 10px 10px 0
    }

    .dateLabel[data-v-20b39bfa] {
        width: 80px
    }

    .message[data-v-20b39bfa],
    .notice[data-v-20b39bfa],
    .regionLabel[data-v-20b39bfa] {
        font-size: 12px
    }

    .btnBox[data-v-20b39bfa] {
        margin-top: 10px;
        float: none
    }

    .hiddenSm[data-v-20b39bfa] {
        display: none
    }

    .flexWrapSm[data-v-20b39bfa] {
        flex-wrap: wrap
    }

    .dataName[data-v-20b39bfa],
    .dataTitle.short[data-v-20b39bfa],
    .dataTitle[data-v-20b39bfa] {
        width: 100%
    }

    .shopNameContent[data-v-20b39bfa] {
        width: auto
    }

    .goodsName[data-v-20b39bfa],
    .price[data-v-20b39bfa] {
        max-width: 120px
    }

    .shopList .dataTitle[data-v-20b39bfa] {
        width: 100%
    }

    .shopList .dataName[data-v-20b39bfa] {
        width: calc(100% - 55px);
        text-align: right
    }

    .dataItem[data-v-20b39bfa] {
        padding: 5px 10px
    }
}

.textareaBox .el-textarea__inner {
    border: none;
    padding: 0;
    margin-bottom: 20px;
    resize: none;
    color: red
}