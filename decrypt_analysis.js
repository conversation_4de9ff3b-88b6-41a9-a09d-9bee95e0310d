// 解密分析脚本
// 用于分析和解密API响应中的data字段

// 响应中的data字段（base64编码的数字数组）
const encryptedData = "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";

console.log("=== 解密分析开始 ===");

// 步骤1: Base64解码
console.log("1. Base64解码:");
const decodedString = atob(encryptedData);
console.log("解码后的字符串:", decodedString);

// 步骤2: 分割成数字数组
console.log("\n2. 分割成数字数组:");
const numberArray = decodedString.split(',').map(num => parseInt(num, 10));
console.log("数字数组长度:", numberArray.length);
console.log("前20个数字:", numberArray.slice(0, 20));

// 步骤3: 尝试不同的解密方法

// 方法1: 直接转换为字符
console.log("\n3. 方法1 - 直接转换为字符:");
try {
    const directString = String.fromCharCode(...numberArray);
    console.log("直接转换结果:", directString);
} catch (e) {
    console.log("直接转换失败:", e.message);
}

// 方法2: XOR解密（尝试不同的密钥）
console.log("\n4. 方法2 - XOR解密:");
const commonKeys = [0x42, 0x88, 0xFF, 0x33, 0x66, 0x99, 0xCC];
commonKeys.forEach(key => {
    try {
        const xorDecrypted = numberArray.map(num => num ^ key);
        const xorString = String.fromCharCode(...xorDecrypted);
        console.log(`XOR密钥 ${key.toString(16)}: ${xorString.substring(0, 50)}...`);
    } catch (e) {
        console.log(`XOR密钥 ${key.toString(16)} 失败:`, e.message);
    }
});

// 方法3: 减法解密（尝试不同的偏移量）
console.log("\n5. 方法3 - 减法解密:");
const offsets = [1, 2, 3, 5, 8, 13, 21, 32, 42, 88];
offsets.forEach(offset => {
    try {
        const subtractDecrypted = numberArray.map(num => num - offset);
        const validChars = subtractDecrypted.filter(num => num >= 0 && num <= 255);
        if (validChars.length === subtractDecrypted.length) {
            const subtractString = String.fromCharCode(...subtractDecrypted);
            console.log(`偏移量 ${offset}: ${subtractString.substring(0, 50)}...`);
        }
    } catch (e) {
        console.log(`偏移量 ${offset} 失败:`, e.message);
    }
});

// 方法4: 模运算解密
console.log("\n6. 方法4 - 模运算解密:");
const modValues = [256, 128, 64, 32];
modValues.forEach(mod => {
    try {
        const modDecrypted = numberArray.map(num => num % mod);
        const modString = String.fromCharCode(...modDecrypted);
        console.log(`模 ${mod}: ${modString.substring(0, 50)}...`);
    } catch (e) {
        console.log(`模 ${mod} 失败:`, e.message);
    }
});

// 方法5: 位运算解密
console.log("\n7. 方法5 - 位运算解密:");
try {
    const bitDecrypted = numberArray.map(num => num & 0xFF);
    const bitString = String.fromCharCode(...bitDecrypted);
    console.log("位与0xFF:", bitString.substring(0, 50) + "...");
} catch (e) {
    console.log("位运算失败:", e.message);
}

// 方法6: 尝试UTF-8解码
console.log("\n8. 方法6 - UTF-8解码:");
try {
    const uint8Array = new Uint8Array(numberArray);
    const utf8String = new TextDecoder('utf-8').decode(uint8Array);
    console.log("UTF-8解码:", utf8String.substring(0, 100) + "...");
} catch (e) {
    console.log("UTF-8解码失败:", e.message);
}

// 方法7: 尝试GBK/GB2312解码（如果支持）
console.log("\n9. 方法7 - 尝试其他编码:");
const encodings = ['gbk', 'gb2312', 'big5', 'iso-8859-1'];
encodings.forEach(encoding => {
    try {
        const uint8Array = new Uint8Array(numberArray);
        const decodedString = new TextDecoder(encoding).decode(uint8Array);
        console.log(`${encoding}解码:`, decodedString.substring(0, 50) + "...");
    } catch (e) {
        console.log(`${encoding}解码失败:`, e.message);
    }
});

// 方法8: 尝试凯撒密码（字符偏移）
console.log("\n10. 方法8 - 凯撒密码:");
for (let shift = 1; shift <= 25; shift++) {
    try {
        const caesarDecrypted = numberArray.map(num => {
            if (num >= 65 && num <= 90) { // 大写字母
                return ((num - 65 - shift + 26) % 26) + 65;
            } else if (num >= 97 && num <= 122) { // 小写字母
                return ((num - 97 - shift + 26) % 26) + 97;
            }
            return num;
        });
        const caesarString = String.fromCharCode(...caesarDecrypted);
        if (caesarString.includes('首都') || caesarString.includes('游历') || caesarString.includes('卷')) {
            console.log(`凯撒偏移 ${shift}: ${caesarString}`);
        }
    } catch (e) {
        // 忽略错误
    }
}

// 方法9: 尝试简单的替换密码
console.log("\n11. 方法9 - 简单替换密码:");
// 尝试一些常见的替换模式
const substitutions = [
    // 可能的字符映射
    {from: 120, to: 233}, // x -> é
    {from: 218, to: 233}, // 其他映射
];

// 方法10: 分析数据模式
console.log("\n12. 方法10 - 数据模式分析:");
console.log("数字范围:", Math.min(...numberArray), "到", Math.max(...numberArray));
console.log("平均值:", numberArray.reduce((a, b) => a + b, 0) / numberArray.length);

// 统计数字频率
const frequency = {};
numberArray.forEach(num => {
    frequency[num] = (frequency[num] || 0) + 1;
});
const sortedFreq = Object.entries(frequency).sort((a, b) => b[1] - a[1]);
console.log("最常见的10个数字:", sortedFreq.slice(0, 10));

// 方法11: 尝试反向操作
console.log("\n13. 方法11 - 反向操作:");
try {
    const reversed = numberArray.reverse();
    const reversedString = String.fromCharCode(...reversed);
    console.log("反向后:", reversedString.substring(0, 50) + "...");
} catch (e) {
    console.log("反向操作失败:", e.message);
}

// 方法12: 尝试分组解密
console.log("\n14. 方法12 - 分组解密:");
const groupSizes = [2, 3, 4, 8];
groupSizes.forEach(size => {
    try {
        const groups = [];
        for (let i = 0; i < numberArray.length; i += size) {
            groups.push(numberArray.slice(i, i + size));
        }
        console.log(`分组大小 ${size}, 组数: ${groups.length}, 第一组: [${groups[0].join(',')}]`);

        // 尝试对每组进行不同的操作
        const groupDecrypted = groups.map(group => {
            // 尝试XOR组内第一个数字
            if (group.length > 1) {
                const key = group[0];
                return group.slice(1).map(num => num ^ key);
            }
            return group;
        }).flat();

        if (groupDecrypted.every(num => num >= 0 && num <= 255)) {
            const groupString = String.fromCharCode(...groupDecrypted);
            console.log(`分组XOR解密 ${size}: ${groupString.substring(0, 30)}...`);
        }
    } catch (e) {
        console.log(`分组 ${size} 失败:`, e.message);
    }
});

console.log("\n=== 解密分析结束 ===");
