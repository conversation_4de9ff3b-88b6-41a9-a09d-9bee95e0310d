// 最终的解密函数 - 用于解密API响应中的data字段
const zlib = require('zlib');

/**
 * 解密API响应中的data字段
 * @param {string} encryptedData - Base64编码的压缩数据
 * @returns {Object} 解密后的JSON对象
 */
function decryptApiResponse(encryptedData) {
    try {
        // 步骤1: Base64解码得到逗号分隔的数字字符串
        const decodedString = Buffer.from(encryptedData, 'base64').toString();
        
        // 步骤2: 分割成数字数组并转换为Buffer
        const numberArray = decodedString.split(',').map(num => parseInt(num, 10));
        const compressedBuffer = Buffer.from(numberArray);
        
        // 步骤3: 使用zlib解压缩
        const decompressed = zlib.inflateSync(compressedBuffer);
        
        // 步骤4: 转换为UTF-8字符串
        const jsonString = decompressed.toString('utf8');
        
        // 步骤5: 解析JSON
        const jsonData = JSON.parse(jsonString);
        
        return {
            success: true,
            data: jsonData,
            originalLength: encryptedData.length,
            compressedLength: compressedBuffer.length,
            decompressedLength: jsonString.length
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            data: null
        };
    }
}

/**
 * 简化版解密函数 - 只返回解密后的数据
 * @param {string} encryptedData - Base64编码的压缩数据
 * @returns {Object|null} 解密后的JSON对象，失败时返回null
 */
function simpleDecrypt(encryptedData) {
    const result = decryptApiResponse(encryptedData);
    return result.success ? result.data : null;
}

// 示例用法
const exampleData = "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";

console.log("=== 解密函数测试 ===");

// 测试完整版解密函数
const result = decryptApiResponse(exampleData);
console.log("解密结果:", result);

if (result.success) {
    console.log("\n解密成功！");
    console.log("原始数据长度:", result.originalLength);
    console.log("压缩数据长度:", result.compressedLength);
    console.log("解压后数据长度:", result.decompressedLength);
    console.log("压缩比:", ((1 - result.compressedLength / result.decompressedLength) * 100).toFixed(2) + "%");
    
    console.log("\n解密后的数据:");
    console.log(JSON.stringify(result.data, null, 2));
}

// 测试简化版解密函数
console.log("\n=== 简化版解密测试 ===");
const simpleResult = simpleDecrypt(exampleData);
if (simpleResult) {
    console.log("简化版解密成功！");
    console.log("商店数据数量:", simpleResult.shopInitData?.length || 0);
    console.log("摊位数据数量:", simpleResult.boothInitData?.length || 0);
    console.log("寄售数据数量:", simpleResult.consignmentInitData?.length || 0);
} else {
    console.log("简化版解密失败");
}

// 导出函数供其他模块使用
module.exports = {
    decryptApiResponse,
    simpleDecrypt
};
