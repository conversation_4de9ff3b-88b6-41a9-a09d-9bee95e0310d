#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
    height: 100%;
    box-sizing: border-box
}

body,
html {
    width: 100%;
    margin: 0;
    padding: 0
}

.box,
body,
html {
    height: 100%;
    background: #f0f0f0;
    box-sizing: border-box
}

.box {
    padding: 20px 0;
    overflow-y: auto
}

.main {
    width: 1080px;
    height: 100%;
    margin: 0 auto 0;
    background: #fff;
    box-sizing: border-box
}

.el-scrollbar .el-scrollbar__bar {
    opacity: 1 !important
}

.fontBoldStroke,
.fontStroke {
    text-shadow: 1px 1px 1px #000, -1px -1px 1px #000, -1px 1px 1px #000, 1px -1px 1px #000
}

.fontBoldStroke {
    font-weight: 700
}

.cursor {
    cursor: pointer
}

@media (max-width: 767px) {
    .box {
        padding: 0
    }

    .main {
        width: 100%
    }
}

@font-face {
    font-family: iconfont;
    src: url(//at.alicdn.com/t/font_3360897_kak3fmq99y.woff2?t=1651239198659) format("woff2"), url(//at.alicdn.com/t/font_3360897_kak3fmq99y.woff?t=1651239198659) format("woff"), url(//at.alicdn.com/t/font_3360897_kak3fmq99y.ttf?t=1651239198659) format("truetype")
}

.iconfont {
    font-family: iconfont !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.icon-pasw-hide:before {
    content: "\e725"
}

.icon-pasw-show:before {
    content: "\e726"
}

.icon-shuju1:before {
    content: "\e613"
}