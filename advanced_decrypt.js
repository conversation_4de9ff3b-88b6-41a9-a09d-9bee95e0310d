// 高级解密分析 - 专门针对游戏数据加密
const encryptedData = "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";

console.log("=== 高级解密分析开始 ===");

// 解码数据
const decodedString = atob(encryptedData);
const numberArray = decodedString.split(',').map(num => parseInt(num, 10));

console.log("数据长度:", numberArray.length);
console.log("数据范围:", Math.min(...numberArray), "到", Math.max(...numberArray));

// 方法1: 尝试RC4解密（常用于游戏）
function rc4(key, data) {
    const s = [];
    for (let i = 0; i < 256; i++) {
        s[i] = i;
    }
    
    let j = 0;
    for (let i = 0; i < 256; i++) {
        j = (j + s[i] + key.charCodeAt(i % key.length)) % 256;
        [s[i], s[j]] = [s[j], s[i]];
    }
    
    const result = [];
    let i = 0;
    j = 0;
    for (let k = 0; k < data.length; k++) {
        i = (i + 1) % 256;
        j = (j + s[i]) % 256;
        [s[i], s[j]] = [s[j], s[i]];
        result.push(data[k] ^ s[(s[i] + s[j]) % 256]);
    }
    return result;
}

console.log("\n1. RC4解密尝试:");
const commonKeys = ['qqsg', 'sgbaodian', 'game', 'key', '123456', 'password', 'secret'];
commonKeys.forEach(key => {
    try {
        const decrypted = rc4(key, numberArray);
        const decryptedString = String.fromCharCode(...decrypted);
        if (decryptedString.includes('首都') || decryptedString.includes('游历') || decryptedString.includes('{') || decryptedString.includes('[')) {
            console.log(`RC4密钥 "${key}": ${decryptedString}`);
        } else {
            console.log(`RC4密钥 "${key}": ${decryptedString.substring(0, 30)}...`);
        }
    } catch (e) {
        console.log(`RC4密钥 "${key}" 失败:`, e.message);
    }
});

// 方法2: 尝试简单的字节序列解密
console.log("\n2. 字节序列解密:");
function trySequenceDecrypt(data, sequence) {
    const result = [];
    for (let i = 0; i < data.length; i++) {
        result.push(data[i] ^ sequence[i % sequence.length]);
    }
    return result;
}

const sequences = [
    [0x42, 0x88], // 简单的2字节序列
    [0x12, 0x34, 0x56], // 3字节序列
    [0xFF, 0x00, 0xFF, 0x00], // 4字节序列
    [0x5A, 0xA5], // 另一个2字节序列
];

sequences.forEach((seq, index) => {
    try {
        const decrypted = trySequenceDecrypt(numberArray, seq);
        const decryptedString = String.fromCharCode(...decrypted);
        console.log(`序列${index + 1} [${seq.map(x => '0x' + x.toString(16)).join(',')}]: ${decryptedString.substring(0, 30)}...`);
    } catch (e) {
        console.log(`序列${index + 1} 失败:`, e.message);
    }
});

// 方法3: 尝试基于位置的解密
console.log("\n3. 基于位置的解密:");
function positionBasedDecrypt(data) {
    return data.map((byte, index) => byte ^ (index % 256));
}

try {
    const posDecrypted = positionBasedDecrypt(numberArray);
    const posString = String.fromCharCode(...posDecrypted);
    console.log("位置解密:", posString.substring(0, 50) + "...");
} catch (e) {
    console.log("位置解密失败:", e.message);
}

// 方法4: 尝试Vigenère密码
console.log("\n4. Vigenère密码:");
function vigenereDecrypt(data, key) {
    const result = [];
    for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        result.push((data[i] - keyChar + 256) % 256);
    }
    return result;
}

const vigenereKeys = ['QQSG', 'GAME', 'KEY'];
vigenereKeys.forEach(key => {
    try {
        const decrypted = vigenereDecrypt(numberArray, key);
        const decryptedString = String.fromCharCode(...decrypted);
        console.log(`Vigenère密钥 "${key}": ${decryptedString.substring(0, 30)}...`);
    } catch (e) {
        console.log(`Vigenère密钥 "${key}" 失败:`, e.message);
    }
});

// 方法5: 尝试解析为JSON结构
console.log("\n5. JSON结构解析尝试:");
// 检查是否有JSON的特征字节
const jsonMarkers = [123, 125, 91, 93, 34, 58, 44]; // { } [ ] " : ,
const hasJsonMarkers = jsonMarkers.some(marker => numberArray.includes(marker));
console.log("包含JSON标记:", hasJsonMarkers);

if (hasJsonMarkers) {
    // 尝试直接解析
    try {
        const directJson = String.fromCharCode(...numberArray);
        console.log("直接JSON尝试:", directJson.substring(0, 100));
    } catch (e) {
        console.log("直接JSON失败");
    }
}

// 方法6: 尝试zlib/deflate解压缩
console.log("\n6. 压缩数据检测:");
// 检查是否是压缩数据的特征
const zlibHeader = [0x78, 0x9C]; // zlib header
const gzipHeader = [0x1F, 0x8B]; // gzip header
const deflateHeader = [0x78, 0xDA]; // deflate header

if (numberArray.length >= 2) {
    const header = [numberArray[0], numberArray[1]];
    console.log("数据头部:", header.map(x => '0x' + x.toString(16)).join(' '));
    
    if (header[0] === 0x78 && (header[1] === 0x9C || header[1] === 0xDA)) {
        console.log("检测到可能的zlib/deflate压缩数据");
    } else if (header[0] === 0x1F && header[1] === 0x8B) {
        console.log("检测到可能的gzip压缩数据");
    } else {
        console.log("未检测到标准压缩格式");
    }
}

console.log("\n=== 高级解密分析结束 ===");
