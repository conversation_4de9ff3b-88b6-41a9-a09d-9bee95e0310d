// 高级十六进制数据解密分析
const crypto = require('crypto');

const hexData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

console.log("=== 高级十六进制数据解密分析 ===");

const buffer = Buffer.from(hexData, 'hex');
console.log("数据长度:", buffer.length);
console.log("数据头部:", buffer.slice(0, 16).toString('hex'));

// 方法1: 尝试AES解密
console.log("\n1. 尝试AES解密:");
const commonKeys = ['qqsg', 'sgbaodian', 'game', 'key', '1234567890123456', 'abcdefghijklmnop'];
const aesAlgorithms = ['aes-128-ecb', 'aes-256-ecb', 'aes-128-cbc', 'aes-256-cbc'];

commonKeys.forEach(keyStr => {
    aesAlgorithms.forEach(algorithm => {
        try {
            let key = Buffer.from(keyStr, 'utf8');
            
            // 调整密钥长度
            if (algorithm.includes('128')) {
                key = key.slice(0, 16);
                if (key.length < 16) {
                    key = Buffer.concat([key, Buffer.alloc(16 - key.length, 0)]);
                }
            } else if (algorithm.includes('256')) {
                key = key.slice(0, 32);
                if (key.length < 32) {
                    key = Buffer.concat([key, Buffer.alloc(32 - key.length, 0)]);
                }
            }
            
            const decipher = crypto.createDecipher(algorithm, key);
            let decrypted = decipher.update(buffer);
            decrypted = Buffer.concat([decrypted, decipher.final()]);
            
            const result = decrypted.toString('utf8');
            console.log(`${algorithm} + "${keyStr}": ${result.substring(0, 50)}...`);
            
            // 检查是否包含JSON
            if (result.includes('{') || result.includes('[')) {
                try {
                    const jsonData = JSON.parse(result);
                    console.log("*** JSON解析成功! ***");
                    console.log(JSON.stringify(jsonData, null, 2));
                } catch (e) {
                    // 继续尝试
                }
            }
        } catch (e) {
            // 忽略错误，继续尝试
        }
    });
});

// 方法2: 尝试XOR解密
console.log("\n2. 尝试XOR解密:");
const xorKeys = [0x42, 0x88, 0xFF, 0x33, 0x66, 0x99, 0xCC, 0x12, 0x34, 0x56];
xorKeys.forEach(key => {
    try {
        const xorDecrypted = Buffer.alloc(buffer.length);
        for (let i = 0; i < buffer.length; i++) {
            xorDecrypted[i] = buffer[i] ^ key;
        }
        
        const result = xorDecrypted.toString('utf8');
        console.log(`XOR密钥 0x${key.toString(16)}: ${result.substring(0, 30)}...`);
        
        // 检查是否包含JSON
        if (result.includes('{') || result.includes('[')) {
            try {
                const jsonData = JSON.parse(result);
                console.log("*** JSON解析成功! ***");
                console.log(JSON.stringify(jsonData, null, 2));
            } catch (e) {
                // 继续尝试
            }
        }
    } catch (e) {
        // 忽略错误
    }
});

// 方法3: 尝试Base64解码后再处理
console.log("\n3. 尝试Base64解码:");
try {
    // 将十六进制当作Base64处理
    const base64Decoded = Buffer.from(hexData, 'base64');
    console.log("Base64解码长度:", base64Decoded.length);
    
    // 尝试zlib解压缩
    const zlib = require('zlib');
    try {
        const inflated = zlib.inflateSync(base64Decoded);
        const result = inflated.toString('utf8');
        console.log("Base64 + zlib解压成功!");
        console.log("解压后内容:", result);
        
        try {
            const jsonData = JSON.parse(result);
            console.log("*** JSON解析成功! ***");
            console.log(JSON.stringify(jsonData, null, 2));
        } catch (e) {
            console.log("JSON解析失败");
        }
    } catch (e) {
        console.log("Base64 + zlib解压失败:", e.message);
    }
} catch (e) {
    console.log("Base64解码失败:", e.message);
}

// 方法4: 尝试分块处理
console.log("\n4. 尝试分块处理:");
const blockSizes = [16, 32, 64, 128];
blockSizes.forEach(blockSize => {
    try {
        const blocks = [];
        for (let i = 0; i < buffer.length; i += blockSize) {
            blocks.push(buffer.slice(i, i + blockSize));
        }
        console.log(`分块大小 ${blockSize}, 块数: ${blocks.length}`);
        
        // 尝试对每个块进行XOR
        const xorResult = Buffer.alloc(buffer.length);
        for (let i = 0; i < blocks.length; i++) {
            const block = blocks[i];
            const key = i % 256; // 使用块索引作为密钥
            for (let j = 0; j < block.length; j++) {
                xorResult[i * blockSize + j] = block[j] ^ key;
            }
        }
        
        const result = xorResult.toString('utf8');
        if (result.includes('{') || result.includes('[')) {
            console.log(`分块XOR ${blockSize}: 包含JSON标记`);
            try {
                const jsonData = JSON.parse(result);
                console.log("*** JSON解析成功! ***");
                console.log(JSON.stringify(jsonData, null, 2));
            } catch (e) {
                // 继续尝试
            }
        }
    } catch (e) {
        // 忽略错误
    }
});

// 方法5: 尝试反转字节序
console.log("\n5. 尝试反转字节序:");
try {
    const reversed = Buffer.from(buffer).reverse();
    const result = reversed.toString('utf8');
    console.log("反转字节序:", result.substring(0, 50) + "...");
    
    if (result.includes('{') || result.includes('[')) {
        try {
            const jsonData = JSON.parse(result);
            console.log("*** JSON解析成功! ***");
            console.log(JSON.stringify(jsonData, null, 2));
        } catch (e) {
            // 继续尝试
        }
    }
} catch (e) {
    console.log("反转字节序失败:", e.message);
}

console.log("\n=== 高级解密分析完成 ===");
